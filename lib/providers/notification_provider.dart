import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/notification_service.dart';
import 'package:healo/providers/auth_state_manager.dart';

/// Notification state model
class NotificationState {
  final bool isInitialized;
  final bool hasPermission;
  final String? fcmToken;
  final bool isLoading;
  final String? error;

  const NotificationState({
    this.isInitialized = false,
    this.hasPermission = false,
    this.fcmToken,
    this.isLoading = false,
    this.error,
  });

  NotificationState copyWith({
    bool? isInitialized,
    bool? hasPermission,
    String? fcmToken,
    bool? isLoading,
    String? error,
  }) {
    return NotificationState(
      isInitialized: isInitialized ?? this.isInitialized,
      hasPermission: hasPermission ?? this.hasPermission,
      fcmToken: fcmToken ?? this.fcmToken,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// Notification provider notifier
class NotificationNotifier extends StateNotifier<NotificationState> {
  final NotificationService _notificationService;
  final Ref _ref;

  NotificationNotifier(this._notificationService, this._ref)
      : super(const NotificationState()) {
    // Watch auth state to handle token refresh when user changes
    _ref.listen(authStateManagerProvider, (previous, next) {
      if (next.status == AuthStatus.authenticated) {
        // User logged in, refresh FCM token
        _refreshToken();
      } else if (next.status == AuthStatus.unauthenticated) {
        // User logged out, clear token from state and Firestore
        state = state.copyWith(fcmToken: null);
        _clearTokenOnLogout();
      }
    });
  }

  /// Initialize notifications
  Future<void> initialize() async {
    if (state.isInitialized) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      await _notificationService.initialize();

      // Get current token
      final token = await _notificationService.getToken();

      state = state.copyWith(
        isInitialized: true,
        hasPermission: token != null,
        fcmToken: token,
        isLoading: false,
      );

      log('Notification provider initialized successfully');
    } catch (e) {
      log('Error initializing notification provider: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh FCM token
  Future<void> _refreshToken() async {
    try {
      final token = await _notificationService.getToken();
      state = state.copyWith(fcmToken: token);
      log('FCM token refreshed in provider: $token');
    } catch (e) {
      log('Error refreshing FCM token in provider: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Get current FCM token
  Future<String?> getToken() async {
    try {
      final token = await _notificationService.getToken();
      state = state.copyWith(fcmToken: token);
      return token;
    } catch (e) {
      log('Error getting FCM token: $e');
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// Clear FCM token on logout
  Future<void> _clearTokenOnLogout() async {
    try {
      await _notificationService.clearFCMToken();
      log('FCM token cleared on logout from provider');
    } catch (e) {
      log('Error clearing FCM token on logout from provider: $e');
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _notificationService.subscribeToTopic(topic);
      log('Subscribed to topic: $topic');
    } catch (e) {
      log('Error subscribing to topic: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _notificationService.unsubscribeFromTopic(topic);
      log('Unsubscribed from topic: $topic');
    } catch (e) {
      log('Error unsubscribing from topic: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    try {
      await _notificationService.clearAllNotifications();
      log('All notifications cleared');
    } catch (e) {
      log('Error clearing notifications: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Notification provider
final notificationProvider = StateNotifierProvider<NotificationNotifier, NotificationState>(
  (ref) => NotificationNotifier(NotificationService(), ref),
);

/// Provider to check if notifications are enabled
final notificationEnabledProvider = Provider<bool>((ref) {
  final notificationState = ref.watch(notificationProvider);
  return notificationState.isInitialized && notificationState.hasPermission;
});

/// Provider to get current FCM token
final fcmTokenProvider = Provider<String?>((ref) {
  final notificationState = ref.watch(notificationProvider);
  return notificationState.fcmToken;
});
