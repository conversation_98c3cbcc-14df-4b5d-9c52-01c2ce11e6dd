import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:healo/services/notification_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  int? _resendToken;

  /// Verifies the phone number and sends OTP
  Future<void> verifyPhoneNumber(
      String phoneNumber, Function(String, int?) codeSent) async {
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      timeout: const Duration(seconds: 60),
      forceResendingToken: _resendToken,

      /// Auto-verification when OTP is detected automatically (e.g., Android)
      verificationCompleted: (PhoneAuthCredential credential) async {
        try {
          await _auth.signInWithCredential(credential);
          log("Auto verification completed");
        } catch (e) {
          log("Auto verification error: $e");
        }
      },

      /// If verification fails (e.g., invalid number)
      verificationFailed: (FirebaseAuthException e) {
        log("Verification failed: ${e.message}");
      },

      /// When the OTP is sent, the verificationId is required for signing in
      codeSent: (String verificationId, int? resendToken) {
        _resendToken = resendToken;
        codeSent(verificationId, resendToken);
        log("OTP sent to $phoneNumber");
      },

      /// If the OTP is not auto-retrieved within the timeout
      codeAutoRetrievalTimeout: (String verificationId) {
        log("Auto retrieval timeout");
      },
    );
  }

  /// Signs in the user with the OTP
  Future<UserCredential?> signInWithOTP(
      String verificationId, String smsCode) async {
    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: verificationId, smsCode: smsCode);

      UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      log("User signed in: ${userCredential.user?.uid}");

      // Initialize notifications after successful login
      try {
        await NotificationService().initialize();
        log("Notifications initialized after login");
      } catch (e) {
        log("Error initializing notifications after login: $e");
      }

      return userCredential;
    } catch (e) {
      log("Sign-in error: $e");
      return null;
    }
  }

  Future<void> logout() async {
    try {
      // Clear FCM token before signing out
      try {
        await NotificationService().clearFCMToken();
        log("FCM token cleared during logout");
      } catch (e) {
        log("Error clearing FCM token during logout: $e");
        // Continue with logout even if this fails
      }

      // Sign out first
      await _auth.signOut();

      // Instead of clearing persistence (which causes the error),
      // we'll terminate and re-initialize Firestore
      try {
        await FirebaseFirestore.instance.terminate();
        await FirebaseFirestore.instance.clearPersistence();
        await FirebaseFirestore.instance.enableNetwork();
      } catch (e) {
        log("Firestore reset error (non-critical): $e");
        // Continue with logout even if this fails
      }

      log("User logged out successfully");
    } catch (e) {
      log("Logout error: $e");
      rethrow; // Rethrow to handle in UI
    }
  }

  Future<bool> deleteAccount() async {
    try {
      // Get the current user
      User? user = _auth.currentUser;
      if (user == null) {
        throw Exception("No user is currently signed in");
      }

      // Delete user data from Firestore first
      String? phoneNumber = user.phoneNumber;
      if (phoneNumber != null) {
        await FirebaseFirestore.instance
            .collection("users")
            .doc(phoneNumber)
            .delete();

        // Delete any other user-specific collections
        await _deleteUserData(phoneNumber);
      }

      // Delete the user account
      await user.delete();

      log("User account deleted successfully");
      return true;
    } catch (e) {
      log("Delete account error: $e");
      return false;
    }
  }

  Future<void> _deleteUserData(String phoneNumber) async {
    try {
      await FirebaseFirestore.instance
          .collection('daily_medication')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('weekly_medication')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('monthly_medication')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('blood_pressure')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('blood_pressure')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('health_data')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('diabetes')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('hba1c')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('kidney')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('liver')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('period_data')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('thyroid')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('water_intake')
          .doc(phoneNumber)
          .delete();

      await FirebaseFirestore.instance
          .collection('users')
          .doc(phoneNumber)
          .delete();
    } catch (e) {
      log("Error deleting user data: $e");
    }
  }
}
