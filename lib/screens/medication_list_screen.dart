import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';

import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:healo/route/route_constants.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/medication_intake_provider.dart';
import 'package:intl/intl.dart';

class MedicationListScreen extends ConsumerStatefulWidget {
  const MedicationListScreen({super.key});

  @override
  ConsumerState<MedicationListScreen> createState() =>
      _MedicationListScreenState();
}

class _MedicationListScreenState extends ConsumerState<MedicationListScreen> {
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(medicationProvider.notifier).fetchDailyMedicationHistory();
      ref
          .read(weeklyMedicationProvider.notifier)
          .fetchWeeklyMedicationHistory();
      ref
          .read(monthlyMedicationProvider.notifier)
          .fetchMonthlyMedicationHistory();

      // Check if coming from notification
      _checkForNotificationData();
    });
  }

  /// Check if screen was opened from notification and show dialog
  void _checkForNotificationData() {
    final args = ModalRoute.of(context)?.settings.arguments;
    if (args != null && args is Map<String, dynamic>) {
      final fromNotification = args['from_notification'] as bool? ?? false;
      if (fromNotification) {
        final medicationName = args['medication_name'] as String? ?? '';
        final time = args['time'] as String? ?? '';
        final dosage = args['dosage'] as String? ?? '';

        if (medicationName.isNotEmpty) {
          // Show dialog after a short delay to ensure screen is built
          Future.delayed(const Duration(milliseconds: 300), () {
            _showMedicationDialog(medicationName, time, dosage);
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final weeklyMedications = ref.watch(weeklyMedicationProvider);
    final dailyMedications = ref.watch(medicationProvider);
    final monthlyMedications = ref.watch(monthlyMedicationProvider);

    final selected = _selectedDate ?? DateTime.now();
    final dateRange = List.generate(5, (i) {
      return selected.subtract(Duration(days: 2 - i));
    });

    List<DailyMedication> filteredDaily = dailyMedications.where((med) {
      try {
        final parts = med.expiryDate.split('/');
        final expiry = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        return expiry.isAfter(selected);
      } catch (_) {
        return true;
      }
    }).toList();

    List<WeeklyMedication> filteredWeekly = weeklyMedications.where((med) {
      try {
        final parts = med.expiryDate.split('/');
        final expiry = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        final isNotExpired = expiry.isAfter(selected);
        final weekday = DateFormat('EEEE').format(selected);
        final isScheduledToday = med.days.contains(weekday);

        return isNotExpired && isScheduledToday;
      } catch (_) {
        return false;
      }
    }).toList();

    List<MonthlyMedication> filteredMonthly = monthlyMedications.where((med) {
      try {
        // Parse expiry
        final parts = med.expiryDate.split('/');
        final expiry = DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
        if (!expiry.isAfter(selected)) return false;

        final selectedDay = selected.day;

        final isScheduledToday = med.dates.any((dateStr) {
          final parts = dateStr?.split('/');
          final day = int.tryParse(parts![0]);
          return day == selectedDay;
        });

        return isScheduledToday;
      } catch (_) {
        return false;
      }
    }).toList();

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "My Medications",
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              _showIntakeHistory();
            },
          ),
        ],
      ),
      body: dailyMedications.isEmpty &&
              weeklyMedications.isEmpty &&
              monthlyMedications.isEmpty
          ? const Center(child: Text("No medications found"))
          : Padding(
              padding: EdgeInsets.only(
                  left: MySize.size15,
                  right: MySize.size15,
                  bottom: MySize.size85),
              child: ListView(
                scrollDirection: Axis.vertical,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _monthName(_selectedDate!.month),
                        style: TextStyle(
                          fontSize: MySize.size22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.calendar_today,
                            color: AppColors.primaryColor),
                        onPressed: () async {
                          final pickedDate = await showDatePicker(
                            context: context,
                            initialDate: _selectedDate ?? DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );

                          if (pickedDate != null) {
                            setState(() {
                              _selectedDate = pickedDate;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: MySize.size74,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: dateRange.map((date) {
                        return Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedDate = date;
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: MySize.size5),
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: AppColors.primaryColor),
                                color: _isSameDate(date, _selectedDate!)
                                    ? AppColors.primaryColor
                                    : Theme.of(context).scaffoldBackgroundColor,
                                borderRadius:
                                    BorderRadius.circular(MySize.size20),
                              ),
                              padding:
                                  EdgeInsets.symmetric(vertical: MySize.size12),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    _weekdayShort(date),
                                    style: TextStyle(
                                      color: _isSameDate(date, _selectedDate!)
                                          ? AppColors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                      fontSize: MySize.size12,
                                    ),
                                  ),
                                  Text(
                                    "${date.day}",
                                    style: TextStyle(
                                      color: _isSameDate(date, _selectedDate!)
                                          ? AppColors.white
                                          : Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  Space.height(20),
                  // Today's Medications Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Today's Medications",
                        style: TextStyle(
                          fontSize: MySize.size20,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                      Text(
                        DateFormat('EEE, MMM d').format(_selectedDate!),
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  Space.height(16),

                  // Today's Medications List
                  ...(_buildTodaysMedications(filteredDaily, filteredWeekly, filteredMonthly)),

                  Space.height(24),

                  // Upcoming Medications Section
                  Text(
                    "Upcoming Medications",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                  Space.height(16),

                  // Tomorrow's medications
                  _buildUpcomingMedications()
                ],
              ),
            ),
      floatingActionButton: SizedBox(
        width: MediaQuery.of(context).size.width * 0.7,
        height: MySize.size50,
        child: FloatingActionButton.extended(
          onPressed: () {
            Navigator.pushNamed(context, addmedicationScreen);
          },
          backgroundColor: AppColors.primaryColor,
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size25),
          ),
          label: Text(
            "Add Medication",
            style: TextStyle(
              color: AppColors.white,
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  /// Build today's medications with new design
  List<Widget> _buildTodaysMedications(List<DailyMedication> daily, List<WeeklyMedication> weekly, List<MonthlyMedication> monthly) {
    List<Widget> medications = [];

    // Add daily medications
    for (var med in daily) {
      medications.add(_buildMedicationCard(
        medicineName: "${med.medicineName} ${med.quantity}${med.unit}",
        frequency: med.frequency,
        dosage: med.dosage,
        timing: med.timing,
        onEdit: () => Navigator.pushNamed(context, addmedicationScreen, arguments: med),
        onDelete: () => _deleteDailyMedication(med),
      ));
    }

    // Add weekly medications
    for (var med in weekly) {
      medications.add(_buildMedicationCard(
        medicineName: "${med.medicineName} ${med.quantity}${med.unit}",
        frequency: med.frequency,
        dosage: med.dosage,
        timing: med.timing,
        onEdit: () => Navigator.pushNamed(context, addmedicationScreen, arguments: med),
        onDelete: () => _deleteWeeklyMedication(med),
      ));
    }

    // Add monthly medications
    for (var med in monthly) {
      medications.add(_buildMedicationCard(
        medicineName: "${med.medicineName} ${med.quantity}${med.unit}",
        frequency: med.frequency,
        dosage: med.dosage,
        timing: med.timing,
        onEdit: () => Navigator.pushNamed(context, addmedicationScreen, arguments: med),
        onDelete: () => _deleteMonthlyMedication(med),
      ));
    }

    if (medications.isEmpty) {
      medications.add(
        Container(
          padding: EdgeInsets.all(MySize.size20),
          child: Center(
            child: Text(
              "No medications scheduled for today",
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: MySize.size14,
              ),
            ),
          ),
        ),
      );
    }

    return medications;
  }

  /// Build individual medication card with new design
  Widget _buildMedicationCard({
    required String medicineName,
    required String frequency,
    required String dosage,
    required List<String> timing,
    required VoidCallback onEdit,
    required VoidCallback onDelete,
  }) {
    // Calculate progress (example: 1/2 taken)
    int totalDoses = timing.length;
    int takenDoses = 1; // This should come from intake tracking

    // Get next dose time
    String nextDoseTime = timing.isNotEmpty ? timing.first : "";

    return Dismissible(
      key: ValueKey(medicineName),
      background: Container(
        alignment: Alignment.centerRight,
        decoration: BoxDecoration(
          color: AppColors.red,
          borderRadius: BorderRadius.circular(MySize.size16),
        ),
        margin: EdgeInsets.only(bottom: MySize.size12),
        padding: EdgeInsets.symmetric(horizontal: MySize.size20),
        child: const Icon(Icons.delete, color: AppColors.white),
      ),
      direction: DismissDirection.endToStart,
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Theme.of(context).cardColor,
            title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
            content: const Text("Are you sure you want to delete this medication?"),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text("Delete", style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        );
      },
      onDismissed: (direction) => onDelete(),
      child: Container(
        margin: EdgeInsets.only(bottom: MySize.size12),
        padding: EdgeInsets.all(MySize.size16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(MySize.size16),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with medicine icon and next dose time
          Row(
            children: [
              // Medicine icon
              Container(
                width: MySize.size48,
                height: MySize.size48,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Icon(
                  Icons.medication,
                  color: Colors.white,
                  size: MySize.size24,
                ),
              ),
              Space.width(12),
              // Medicine details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicineName,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                    Space.height(2),
                    Text(
                      "$totalDoses Times Daily",
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),
              // Next dose time
              if (nextDoseTime.isNotEmpty)
                Text(
                  "Next: $nextDoseTime",
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),

          Space.height(16),

          // Progress bar
          Row(
            children: [
              Expanded(
                child: Container(
                  height: MySize.size6,
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(MySize.size3),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: takenDoses / totalDoses,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        borderRadius: BorderRadius.circular(MySize.size3),
                      ),
                    ),
                  ),
                ),
              ),
              Space.width(8),
              Text(
                "$takenDoses/$totalDoses Taken",
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),

          Space.height(12),

          // Timing details
          Row(
            children: [
              // Taken status
              Container(
                padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.check, color: Colors.white, size: MySize.size12),
                    Space.width(4),
                    Text(
                      "8:00 Am (Taken)",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: MySize.size10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Space.width(8),
              // Due status
              Container(
                padding: EdgeInsets.symmetric(horizontal: MySize.size8, vertical: MySize.size4),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.size12),
                  border: Border.all(color: AppColors.primaryColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.schedule, color: AppColors.primaryColor, size: MySize.size12),
                    Space.width(4),
                    Text(
                      "$nextDoseTime (Due)",
                      style: TextStyle(
                        color: AppColors.primaryColor,
                        fontSize: MySize.size10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    ),
    );
  }

  /// Build upcoming medications section
  Widget _buildUpcomingMedications() {
    final tomorrow = _selectedDate!.add(const Duration(days: 1));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tomorrow header
        Text(
          "Tomorrow, ${DateFormat('MMMM d').format(tomorrow)}",
          style: TextStyle(
            fontSize: MySize.size16,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
        Space.height(12),

        // Upcoming medication items
        _buildUpcomingMedicationItem("Lumia 60K", "8:00 Am, 2:00 Pm"),
        Space.height(8),
        _buildUpcomingMedicationItem("Amryl 2MG", "4:00 Am, 7:00 Pm"),
      ],
    );
  }

  /// Build upcoming medication item
  Widget _buildUpcomingMedicationItem(String medicineName, String timing) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: MySize.size12, vertical: MySize.size8),
      decoration: BoxDecoration(
        color: AppColors.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.medication_outlined,
            color: AppColors.primaryColor,
            size: MySize.size16,
          ),
          Space.width(8),
          Text(
            "$medicineName - $timing",
            style: TextStyle(
              fontSize: MySize.size14,
              color: AppColors.primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Delete daily medication with confirmation
  Future<void> _deleteDailyMedication(DailyMedication med) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
        content: const Text("Are you sure you want to delete this medication?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text("Delete", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(medicationProvider.notifier).deleteDailyMedication(med.medicineName);
    }
  }

  /// Delete weekly medication with confirmation
  Future<void> _deleteWeeklyMedication(WeeklyMedication med) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
        content: const Text("Are you sure you want to delete this medication?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text("Delete", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(weeklyMedicationProvider.notifier).deleteWeeklyMedication(med.medicineName);
    }
  }

  /// Delete monthly medication with confirmation
  Future<void> _deleteMonthlyMedication(MonthlyMedication med) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Confirm", style: TextStyle(color: Theme.of(context).textTheme.bodyLarge?.color)),
        content: const Text("Are you sure you want to delete this medication?"),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text("Cancel", style: TextStyle(color: AppColors.primaryColor)),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text("Delete", style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await ref.read(monthlyMedicationProvider.notifier).deleteMonthlyMedication(med.medicineName);
    }
  }

  String _monthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  bool _isSameDate(DateTime a, DateTime b) =>
      a.year == b.year && a.month == b.month && a.day == b.day;

  String _weekdayShort(DateTime date) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.weekday % 7];
  }

  /// Show medication intake history dialog
  void _showIntakeHistory() async {
    try {
      // Fetch all intake records
      await ref.read(medicationIntakeProvider.notifier).fetchAllIntakes();
      final intakes = ref.read(medicationIntakeProvider);

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          title: Text(
            'Medication Intake History',
            style: TextStyle(
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: intakes.isEmpty
                ? const Center(child: Text('No intake records found'))
                : ListView.builder(
                    itemCount: intakes.length,
                    itemBuilder: (context, index) {
                      final intake = intakes[index];
                      return Card(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        child: ListTile(
                          title: Text(
                            intake.medicationName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                          subtitle: Text(
                            '${intake.date} at ${intake.time}',
                            style: TextStyle(
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                          trailing: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: intake.status == 'taken'
                                  ? Colors.green
                                  : intake.status == 'skipped'
                                      ? Colors.orange
                                      : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              intake.status.toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Close',
                style: TextStyle(color: AppColors.primaryColor),
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading intake history: $e')),
        );
      }
    }
  }

  /// Show medication dialog with Taken/Skip options
  void _showMedicationDialog(String medicationName, String time, String dosage) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size15),
          ),
          title: Row(
            children: [
              Icon(
                Icons.medication,
                color: AppColors.primaryColor,
                size: MySize.size24,
              ),
              Space.width(10),
              Expanded(
                child: Text(
                  'Medication Reminder',
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Time to take your medication:',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
              Space.height(10),
              Container(
                padding: EdgeInsets.all(MySize.size12),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicationName,
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                    if (dosage.isNotEmpty) ...[
                      Space.height(4),
                      Text(
                        'Dosage: $dosage',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    ],
                    if (time.isNotEmpty) ...[
                      Space.height(4),
                      Text(
                        'Time: $time',
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Space.height(15),
              Text(
                'Did you take this medication?',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await _recordMedicationAction(medicationName, time, 'skipped');
                    },
                    icon: const Icon(Icons.close, size: 18),
                    label: const Text('Skip'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black,
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                      ),
                    ),
                  ),
                ),
                Space.width(10),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await _recordMedicationAction(medicationName, time, 'taken');
                    },
                    icon: const Icon(Icons.check, size: 18),
                    label: const Text('Taken'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: MySize.size12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Record medication action to Firebase
  Future<void> _recordMedicationAction(String medicationName, String time, String status) async {
    try {
      if (status == 'taken') {
        await ref.read(medicationIntakeProvider.notifier).markMedicationTaken(medicationName, time);
      } else {
        await ref.read(medicationIntakeProvider.notifier).markMedicationSkipped(medicationName, time);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$medicationName marked as $status'),
            backgroundColor: status == 'taken' ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recording medication: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
