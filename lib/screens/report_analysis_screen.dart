import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/report_model.dart';
import 'package:intl/intl.dart';

class ReportAnalysisScreen extends ConsumerWidget {
  final Report report;

  const ReportAnalysisScreen({
    super.key,
    required this.report,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analysis = report.analysis;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(title: "Report Analysis"),
      body: analysis == null
          ? _buildNoAnalysisView(context)
          : _buildAnalysisView(context, analysis),
    );
  }

  Widget _buildNoAnalysisView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            size: MySize.size50,
            color: AppColors.primaryColor,
          ),
          Space.height(20),
          Text(
            "No analysis available for this report",
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Space.height(10),
          Text(
            "The AI is still processing this report or an error occurred",
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: MySize.size14,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisView(BuildContext context, dynamic analysis) {
    final dateFormat = DateFormat('dd MMM yyyy, hh:mm a');

    return SingleChildScrollView(
      padding: EdgeInsets.all(MySize.size16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Report info card
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(MySize.size10),
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withAlpha(50),
                  blurRadius: 10,
                  offset: const Offset(2, 6),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(MySize.size16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionHeader(context, "Report Information", Icons.description),
                  Space.height(10),
                  _infoRow(context, "Name", report.name),
                  _infoRow(
                    context,
                    "Uploaded",
                    dateFormat.format(report.uploadedAt)
                  ),
                  _infoRow(
                    context,
                    "Analyzed",
                    dateFormat.format(analysis.analyzedAt)
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 20),

          // Summary section
          _buildSectionHeader(context, "Summary", Icons.summarize),
          SizedBox(height: 10),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(MySize.size10),
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryColor.withAlpha(50),
                  blurRadius: 10,
                  offset: const Offset(2, 6),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(MySize.size16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: Colors.amber,
                        size: MySize.size20,
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          analysis.summary,
                          style: TextStyle(
                            fontSize: MySize.size14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 20),

          // Key findings section
          if (analysis.keyFindings != null && analysis.keyFindings!.isNotEmpty) ...[
            _buildSectionHeader(context, "Key Findings", Icons.find_in_page),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(MySize.size10),
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryColor.withAlpha(50),
                    blurRadius: 10,
                    offset: const Offset(2, 6),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: analysis.keyFindings!.entries.toList().asMap().entries.map<Widget>((indexedEntry) {
                    final entry = indexedEntry.value;
                    final isLast = indexedEntry.key == analysis.keyFindings!.length - 1;
                    return _buildFindingItem(context, entry.key, entry.value, isLast);
                  }).toList(),
                ),
              ),
            ),

            SizedBox(height: 20),
          ],

          // Recommended Tests section
          if (analysis.recommendedTests != null && analysis.recommendedTests!.isNotEmpty) ...[
            _buildSectionHeader(context, "Recommended Tests", Icons.science),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(MySize.size10),
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryColor.withAlpha(50),
                    blurRadius: 10,
                    offset: const Offset(2, 6),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: analysis.recommendedTests!.asMap().entries.map<Widget>(
                    (entry) => _buildRecommendationItem(
                      context,
                      entry.key + 1,
                      entry.value,
                      entry.key == analysis.recommendedTests!.length - 1,
                    ),
                  ).toList(),
                ),
              ),
            ),

            SizedBox(height: 20),
          ],

          // Recommended Medications section
          if (analysis.recommendedMedications != null && analysis.recommendedMedications!.isNotEmpty) ...[
            _buildSectionHeader(context, "Recommended Medications", Icons.medication),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(MySize.size10),
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryColor.withAlpha(50),
                    blurRadius: 10,
                    offset: const Offset(2, 6),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: analysis.recommendedMedications!.asMap().entries.map<Widget>(
                    (entry) => _buildRecommendationItem(
                      context,
                      entry.key + 1,
                      entry.value,
                      entry.key == analysis.recommendedMedications!.length - 1,
                    ),
                  ).toList(),
                ),
              ),
            ),

            SizedBox(height: 20),
          ],

          // Lifestyle Recommendations section
          if (analysis.lifestyleRecommendations != null && analysis.lifestyleRecommendations!.isNotEmpty) ...[
            _buildSectionHeader(context, "Lifestyle Recommendations", Icons.self_improvement),
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(MySize.size10),
                color: Theme.of(context).scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryColor.withAlpha(50),
                    blurRadius: 10,
                    offset: const Offset(2, 6),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(MySize.size16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: analysis.lifestyleRecommendations!.asMap().entries.map<Widget>(
                    (entry) => _buildRecommendationItem(
                      context,
                      entry.key + 1,
                      entry.value,
                      entry.key == analysis.lifestyleRecommendations!.length - 1,
                    ),
                  ).toList(),
                ),
              ),
            ),

            SizedBox(height: 20),
          ],

          SizedBox(height: 10),

          // Disclaimer
          Container(
            padding: EdgeInsets.all(MySize.size12),
            decoration: BoxDecoration(
              color: Colors.amber.withAlpha(51),
              borderRadius: BorderRadius.circular(MySize.size10),
              border: Border.all(color: Colors.amber),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.amber),
                    SizedBox(width: 10),
                    Text(
                      "Disclaimer",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: MySize.size14,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 5),
                Text(
                  "This analysis is generated by AI and should not replace professional medical advice. The recommended tests and medications are suggestions based on the report content and may not be appropriate for all patients. Always consult with a healthcare provider for proper diagnosis, treatment, and medication guidance.",
                  style: TextStyle(fontSize: MySize.size12),
                ),
              ],
            ),
          ),

          SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _infoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: MySize.size4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: MySize.size100,
            child: Text(
              "$label:",
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: MySize.size14,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: MySize.size14,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(MySize.size8),
          decoration: BoxDecoration(
            color: AppColors.primaryColorWithAlpha30,
            borderRadius: BorderRadius.circular(MySize.size8),
          ),
          child: Icon(
            icon,
            color: AppColors.primaryColor,
            size: MySize.size20,
          ),
        ),
        Space.width(10),
        Text(
          title,
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildFindingItem(BuildContext context, String title, String description, bool isLast) {
    // Determine severity level based on keywords in the description
    final severityLevel = _getSeverityLevel(description);
    final severityColor = _getSeverityColor(severityLevel);
    final severityIcon = _getSeverityIcon(severityLevel);

    // Format the title properly - capitalize first letter and replace underscores with spaces
    String formattedTitle = title;
    if (title.isNotEmpty && !RegExp(r'^finding\d+$', caseSensitive: false).hasMatch(title)) {
      formattedTitle = title
          .replaceAll('_', ' ')
          .replaceAll('-', ' ')
          .split(' ')
          .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : '')
          .join(' ')
          .trim();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: MySize.size8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(MySize.size6),
                decoration: BoxDecoration(
                  color: severityColor.withAlpha(40),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  severityIcon,
                  color: severityColor,
                  size: MySize.size14,
                ),
              ),
              Space.width(10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Show formatted title if it's not a generic finding key
                    if (title.isNotEmpty && !RegExp(r'^finding\d+$', caseSensitive: false).hasMatch(title))
                      Text(
                        formattedTitle,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: MySize.size15,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                      ),
                    if (title.isNotEmpty && !RegExp(r'^finding\d+$', caseSensitive: false).hasMatch(title))
                      Space.height(4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (!isLast) Divider(height: MySize.size1, color: AppColors.primaryColorWithAlpha30),
      ],
    );
  }

  int _getSeverityLevel(String text) {
    final lowerCaseText = text.toLowerCase();

    // High severity keywords
    if (lowerCaseText.contains('critical') ||
        lowerCaseText.contains('severe') ||
        lowerCaseText.contains('urgent') ||
        lowerCaseText.contains('immediate attention') ||
        lowerCaseText.contains('significantly elevated') ||
        lowerCaseText.contains('dangerously')) {
      return 3; // High severity
    }

    // Medium severity keywords
    if (lowerCaseText.contains('elevated') ||
        lowerCaseText.contains('abnormal') ||
        lowerCaseText.contains('concerning') ||
        lowerCaseText.contains('moderate') ||
        lowerCaseText.contains('attention') ||
        lowerCaseText.contains('higher than normal')) {
      return 2; // Medium severity
    }

    // Low severity keywords
    if (lowerCaseText.contains('mild') ||
        lowerCaseText.contains('slight') ||
        lowerCaseText.contains('minor') ||
        lowerCaseText.contains('borderline') ||
        lowerCaseText.contains('slightly')) {
      return 1; // Low severity
    }

    // Normal or informational
    if (lowerCaseText.contains('normal') ||
        lowerCaseText.contains('healthy') ||
        lowerCaseText.contains('good') ||
        lowerCaseText.contains('optimal') ||
        lowerCaseText.contains('within range')) {
      return 0; // Normal
    }

    return 1; // Default to low severity if no keywords match
  }

  Color _getSeverityColor(int severityLevel) {
    switch (severityLevel) {
      case 0:
        return Colors.green;
      case 1:
        return Colors.blue;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  IconData _getSeverityIcon(int severityLevel) {
    switch (severityLevel) {
      case 0:
        return Icons.check_circle;
      case 1:
        return Icons.info;
      case 2:
        return Icons.warning;
      case 3:
        return Icons.error;
      default:
        return Icons.info;
    }
  }

  Widget _buildRecommendationItem(BuildContext context, int index, String recommendation, bool isLast) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: MySize.size8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(MySize.size6),
                decoration: BoxDecoration(
                  color: AppColors.primaryColorWithAlpha30,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getIconForRecommendation(recommendation),
                  color: AppColors.primaryColor,
                  size: MySize.size14,
                ),
              ),
              Space.width(10),
              Expanded(
                child: Text(
                  recommendation,
                  style: TextStyle(
                    fontSize: MySize.size14,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (!isLast) Divider(height: MySize.size1, color: AppColors.primaryColorWithAlpha30),
      ],
    );
  }

  IconData _getIconForRecommendation(String recommendation) {
    final lowerCaseText = recommendation.toLowerCase();

    // For tests
    if (lowerCaseText.contains('blood') || lowerCaseText.contains('test')) {
      return Icons.opacity;
    } else if (lowerCaseText.contains('scan') || lowerCaseText.contains('mri') || lowerCaseText.contains('ct')) {
      return Icons.scanner;
    } else if (lowerCaseText.contains('x-ray') || lowerCaseText.contains('xray')) {
      return Icons.radio;
    } else if (lowerCaseText.contains('ultrasound')) {
      return Icons.waves;
    }

    // For medications
    if (lowerCaseText.contains('medication') || lowerCaseText.contains('drug') || lowerCaseText.contains('pill')) {
      return Icons.medication;
    } else if (lowerCaseText.contains('supplement') || lowerCaseText.contains('vitamin')) {
      return Icons.spa;
    } else if (lowerCaseText.contains('injection') || lowerCaseText.contains('insulin')) {
      return Icons.vaccines;
    }

    // For lifestyle
    if (lowerCaseText.contains('exercise') || lowerCaseText.contains('physical') || lowerCaseText.contains('activity')) {
      return Icons.directions_run;
    } else if (lowerCaseText.contains('diet') || lowerCaseText.contains('food') || lowerCaseText.contains('nutrition')) {
      return Icons.restaurant;
    } else if (lowerCaseText.contains('sleep')) {
      return Icons.bedtime;
    } else if (lowerCaseText.contains('stress') || lowerCaseText.contains('meditation')) {
      return Icons.self_improvement;
    } else if (lowerCaseText.contains('water') || lowerCaseText.contains('hydration')) {
      return Icons.water_drop;
    }

    // Default icon
    return Icons.check_circle;
  }
}
